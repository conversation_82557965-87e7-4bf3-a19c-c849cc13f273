import { useState, useRef, useEffect, useCallback } from "react";
import {
	IconUpload,
	IconCheck,
	IconX,
	IconInfoCircle,
	IconArrowLeft,
} from "@tabler/icons-react";
import {
	Button,
	Paper,
	Title,
	Text,
	Stack,
	ThemeIcon,
	Box,
	Flex,
	Progress,
	ProgressLabel,
	Group,
	ActionIcon,
} from "@mantine/core";
import { notifications } from "@mantine/notifications";
import apiClient from "../config/axios";
import axios, { isAxiosError } from "axios";
import type { videoDataType } from "../types";
import { useAuth } from "../contexts/AuthContext";
import {
	ProfileStatusMapping,
	VIDEO_UPLOAD_MAX_SIZE_IN_MB,
	videoTranscriptionStatusMapping,
} from "../constants";
import RecordVideoModal from "./video-record/RecordVideoModal";
import VideoInstructionsModal from "./video-instruction/VideoInstructionsModal";
import FullScreenLoader from "./FullScreenLoader";

interface VideoUploadProps {
	description: string;
	videoType: videoDataType;
	setHasUnsavedChanges?: (hasUnsavedChanges: boolean) => void;
	onUploadSuccess?: () => void;
	setIsStepBusy?: (isBusy: boolean) => void;
}

const useVideoPolling = ({
	videoType,
	onUploadSuccess,
	setIsStepBusy,
}: {
	videoType: videoDataType;
	onUploadSuccess?: () => void;
	setIsStepBusy?: (isBusy: boolean) => void;
}) => {
	const pollingRef = useRef<number | null>(null);

	const stopPolling = useCallback(() => {
		if (pollingRef.current) {
			clearTimeout(pollingRef.current);
			pollingRef.current = null;
		}
		setIsStepBusy?.(false);
	}, [setIsStepBusy]);

	const startPolling = useCallback(() => {
		stopPolling(); // Ensure no multiple pollings
		setIsStepBusy?.(true);
		console.log("Start polling");
		const poll = async () => {
			try {
				const response = await apiClient.get(
					`/api/videos/upload-status?videoType=${videoType}&polling=true`
				);
				const { transcriptionStatus } = response.data;

				if (
					transcriptionStatus ===
					videoTranscriptionStatusMapping.completed
				) {
					stopPolling();
					onUploadSuccess?.();
				} else if (
					transcriptionStatus !==
						videoTranscriptionStatusMapping.pending &&
					transcriptionStatus !==
						videoTranscriptionStatusMapping.processing
				) {
					// Handle other terminal states (e.g., failed)
					stopPolling();
					notifications.show({
						title: "Video Processing Failed",
						message:
							"Something went wrong while processing your video. Please try re-uploading.",
						color: "red",
					});
				} else {
					pollingRef.current = window.setTimeout(poll, 10000);
				}
			} catch (error) {
				console.error("Error polling video status:", error);
				stopPolling(); // Stop on network or other errors
			}
		};

		// Start first poll after an initial delay
		pollingRef.current = window.setTimeout(poll, 10000);
	}, [stopPolling, setIsStepBusy, videoType, onUploadSuccess]);

	useEffect(() => {
		// Cleanup on unmount
		return () => {
			stopPolling();
		};
	}, [stopPolling]);

	return { startPolling, stopPolling };
};

const VideoUpload: React.FC<VideoUploadProps> = ({
	description,
	videoType,
	setHasUnsavedChanges,
	onUploadSuccess,
	setIsStepBusy,
}) => {
	const { user, fetchUser } = useAuth();
	const [loading, setLoading] = useState<boolean>(false);
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [videoUrl, setVideoUrl] = useState<string>("");
	const [isUploading, setIsUploading] = useState<boolean>(false);
	const [uploadCompleted, setUploadCompleted] = useState<boolean>(false);
	const [progress, setProgress] = useState<number>(0);
	const fileInputRef = useRef<HTMLInputElement>(null);
	const dragRef = useRef<HTMLDivElement>(null);
	const [onRecordClick, setOnRecordClick] = useState<boolean>(false);
	const isUploadingRef = useRef<boolean>(false);
	const abortControllerRef = useRef<AbortController | null>(null);
	const [showInstructions, setShowInstructions] = useState<boolean>(false);
	const [prevVideoUrl, setPrevVideoUrl] = useState<string>("");
	const [isReuploading, setIsReuploading] = useState(false);
	const { startPolling, stopPolling } = useVideoPolling({
		videoType,
		onUploadSuccess,
		setIsStepBusy,
	});

	const fetchUploadStatus = async () => {
		try {
			setLoading(true);
			const response = await apiClient.get(
				`/api/videos/upload-status?videoType=${videoType}`
			);
			const isVideoUploaded = !!response.data.videoUrl;
			const transcriptionStatus = response.data.transcriptionStatus;
			if (
				transcriptionStatus ===
					videoTranscriptionStatusMapping.pending ||
				transcriptionStatus ===
					videoTranscriptionStatusMapping.processing
			) {
				setUploadCompleted(isVideoUploaded);
				setShowInstructions(false);
				if (
					isVideoUploaded &&
					user?.profileStatus ===
						ProfileStatusMapping.ChangesRequested
				) {
					startPolling();
				}
			} else if (
				transcriptionStatus ===
				videoTranscriptionStatusMapping.completed
			) {
				setPrevVideoUrl(response.data.videoUrl);
				setShowInstructions(false);
				stopPolling();
			} else {
				setShowInstructions(true);
			}
		} catch (error) {
			if (isAxiosError(error)) {
				notifications.show({
					title: "Video Status error",
					message:
						error.response?.data?.message ??
						error.message ??
						"Failed to fetch upload status",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Video Status Error",
					message: "Failed to fetch video status",
					color: "red",
				});
			}
		} finally {
			setLoading(false);
		}
	};

	const handleFileSelect = async (file: File) => {
		if (!file) return;

		if (!file.type.startsWith("video/")) {
			notifications.show({
				title: "Invalid File Type",
				message: "Please select a video file",
				color: "red",
				icon: <IconX />,
			});
			return;
		}

		const maxSizeInBytes = VIDEO_UPLOAD_MAX_SIZE_IN_MB * 1024 * 1024;
		if (file.size > maxSizeInBytes) {
			notifications.show({
				title: "File Too Large",
				message: `Please select a video file smaller than ${VIDEO_UPLOAD_MAX_SIZE_IN_MB}MB`,
				color: "red",
				icon: <IconX />,
			});
			return;
		}

		setHasUnsavedChanges?.(true);
		setSelectedFile(file);
		const url = URL.createObjectURL(file);
		setVideoUrl(url);
		if (isReuploading) {
			setIsReuploading(false);
		}
	};

	const handleUpload = async () => {
		if (!selectedFile) {
			notifications.show({
				title: "Video not selected",
				message: "Please select a video file to upload",
				color: "red",
			});
			return;
		}

		try {
			setIsUploading(true);
			isUploadingRef.current = true;

			const controller = new AbortController();
			abortControllerRef.current = controller;

			const { data } = await apiClient.post(
				"/api/videos/upload",
				{
					videoType,
					contentType: selectedFile.type,
					fileName: selectedFile.name,
				},
				{
					signal: controller.signal,
				}
			);

			const { signedUrl, videoId } = data;

			await axios.put(signedUrl, selectedFile, {
				headers: {
					"Content-Type": selectedFile.type,
				},
				signal: controller.signal,
				onUploadProgress: progressEvent => {
					if (progressEvent.total) {
						const percentCompleted = Math.round(
							(progressEvent.loaded * 100) / progressEvent.total
						);
						setProgress(percentCompleted);
					}
				},
			});
			await apiClient.post(
				"/api/videos/upload/success",
				{
					videoType,
					videoId,
				},
				{
					signal: controller.signal,
				}
			);
			setIsUploading(false);
			isUploadingRef.current = false;
			setUploadCompleted(true);
			setHasUnsavedChanges?.(false);
			abortControllerRef.current = null;
			setSelectedFile(null);
			setVideoUrl("");
			setProgress(0);

			await fetchUser();
			notifications.show({
				title: "Upload Successful",
				message:
					"Once the video data is extracted, we will notify you by email.",
				color: "green",
			});
			if (user?.profileStatus === ProfileStatusMapping.ChangesRequested) {
				startPolling();
			}
		} catch (error) {
			setIsUploading(false);
			isUploadingRef.current = false;
			abortControllerRef.current = null;
			if (axios.isCancel(error)) {
				notifications.show({
					title: "Upload Cancelled",
					message: "Video upload was cancelled",
					color: "yellow",
				});
				return;
			}
			if (isAxiosError(error)) {
				notifications.show({
					title: "Error",
					message:
						error.response?.data?.message ||
						"Failed to upload video.",
					color: "red",
				});
			} else {
				notifications.show({
					title: "Error",
					message: "Failed to upload video.",
					color: "red",
				});
			}
		}
	};

	useEffect(() => {
		if (videoUrl) {
			console.log("Updated videoUrl: ", videoUrl);
		}

		return () => {
			if (videoUrl) {
				URL.revokeObjectURL(videoUrl);
			}
		};
	}, [videoUrl]);

	const cancelUpload = useCallback(() => {
		const controller = abortControllerRef.current;
		if (controller) {
			controller.abort();
			abortControllerRef.current = null;
			setIsUploading(false);
			isUploadingRef.current = false;
			setProgress(0);
			setHasUnsavedChanges?.(false);

			notifications.show({
				title: "Upload Cancelled",
				message: "Video upload was cancelled",
				color: "yellow",
			});
		}
	}, [setHasUnsavedChanges]);

	useEffect(() => {
		fetchUploadStatus();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	useEffect(() => {
		const handleBeforeUnload = (event: BeforeUnloadEvent) => {
			if (isUploadingRef.current && abortControllerRef.current) {
				event.preventDefault();

				// Cancel the upload immediately
				abortControllerRef.current.abort();
				setIsUploading(false);
				isUploadingRef.current = false;
				abortControllerRef.current = null;
				setHasUnsavedChanges?.(false);
			}
		};

		window.addEventListener("beforeunload", handleBeforeUnload);

		return () => {
			window.removeEventListener("beforeunload", handleBeforeUnload);
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}
		};
	}, [setHasUnsavedChanges]);

	const handleDrop = (e: React.DragEvent) => {
		e.preventDefault();
		const file = e.dataTransfer.files[0];
		handleFileSelect(file);
	};

	const handleDragOver = (e: React.DragEvent) => {
		e.preventDefault();
	};

	const handleBackClick = () => {
		if (isUploading) {
			cancelUpload();
		}
		setSelectedFile(null);
		setHasUnsavedChanges?.(false);
		setVideoUrl("");
	};

	const handleCloseInstructions = () => {
		setShowInstructions(false);
	};

	if (loading) return <FullScreenLoader />;

	return (
		<div style={{ height: "100%" }}>
			<VideoInstructionsModal
				opened={showInstructions}
				onClose={handleCloseInstructions}
				videoType={videoType}
			/>

			<div style={{ height: "100%", width: "100%" }}>
				<Flex align={"center"} justify={"center"} h={"100%"} w={"100%"}>
					{uploadCompleted ? (
						<Paper
							shadow="sm"
							p="xl"
							radius="md"
							withBorder
							h={"26rem"}
							w={"50%"}
							style={{ minHeight: "26rem" }}
						>
							<Stack align="center" justify="center" h="100%">
								<ThemeIcon
									size="xl"
									variant="light"
									color="green"
									radius="xl"
								>
									<IconCheck size="2rem" />
								</ThemeIcon>
								<Title
									order={3}
									style={{ textAlign: "center" }}
								>
									{description}{" "}
									<span
										style={{
											color: "green",
											display: "block",
										}}
									>
										Upload Successful!
									</span>
								</Title>
								<Text c="dimmed" ta="center">
									Thanks for uploading your video! We're
									processing it to extract the information for
									your profile.
								</Text>
								<Text c="dimmed" ta="center" size="sm">
									This usually takes 1–2 minutes. You’ll be
									automatically redirected to next stage once
									it’s ready, and we’ll also notify you by
									email.
								</Text>
							</Stack>
						</Paper>
					) : selectedFile || videoUrl ? (
						<Paper
							shadow="sm"
							p="xl"
							radius="md"
							withBorder
							w={"50%"}
						>
							<Stack h="100%">
								<Title order={2}>{description}</Title>
								<Text c="dimmed" size="sm" mb={2}>
									<Text fw={600} display={"inline"} mr={4}>
										Please note:
									</Text>
									The recorded/uploaded video will be part of
									your profile and will be visible to other
									community members
								</Text>
								<Box
									style={{
										backgroundColor: "black",
										borderRadius:
											"var(--mantine-radius-md)",
										overflow: "hidden",
										width: "100%",
										flexGrow: 1,
										height: "24rem",
										maxHeight: "calc(50vh - 70px)",
									}}
								>
									<video
										controls
										src={videoUrl}
										style={{
											width: "100%",
											height: "100%",
											objectFit: "contain",
										}}
									>
										Your browser does not support the video
										tag.
									</video>
								</Box>
								{isUploading && (
									<Stack mt="md">
										<Progress.Root
											size={"xl"}
											radius={"lg"}
										>
											<Progress.Section
												value={progress}
												striped
												animated
											>
												<ProgressLabel>
													{progress}%
												</ProgressLabel>
											</Progress.Section>
										</Progress.Root>
									</Stack>
								)}
								{!isUploading && (
									<Flex
										align="center"
										justify="flex-end"
										mt="md"
										gap={"md"}
									>
										<Button
											onClick={handleBackClick}
											variant="outline"
											disabled={
												isUploading ||
												(!selectedFile && !videoUrl)
											}
										>
											Cancel
										</Button>
										<Button
											onClick={handleUpload}
											disabled={
												isUploading ||
												(!selectedFile && !videoUrl)
											}
										>
											{isUploading
												? "Uploading..."
												: "Upload Video"}
										</Button>
									</Flex>
								)}
							</Stack>
						</Paper>
					) : prevVideoUrl && !isReuploading ? (
						<Paper
							shadow="sm"
							p="xl"
							radius="md"
							withBorder
							w={"50%"}
						>
							<Stack h="100%">
								<Title order={2}>{description}</Title>
								<Text c="dimmed" size="sm" mb={2}>
									The previously uploaded video is now part of
									your profile and visible to all community
									members.
								</Text>
								<Box
									style={{
										backgroundColor: "black",
										borderRadius:
											"var(--mantine-radius-md)",
										overflow: "hidden",
										width: "100%",
										flexGrow: 1,
										height: "24rem",
										maxHeight: "calc(50vh - 70px)",
									}}
								>
									<video
										controls
										src={prevVideoUrl}
										style={{
											width: "100%",
											height: "100%",
											objectFit: "contain",
										}}
									>
										Your browser does not support the video
										tag.
									</video>
								</Box>
								{user?.profileStatus ===
									ProfileStatusMapping.ChangesRequested && (
									<Flex align="center" justify="flex-end">
										<Button
											onClick={() =>
												setIsReuploading(true)
											}
										>
											Re-upload Video
										</Button>
									</Flex>
								)}
							</Stack>
						</Paper>
					) : (
						<Paper
							shadow="sm"
							p="xl"
							radius="md"
							withBorder
							w={"50%"}
						>
							<Stack h="100%">
								<Flex
									align={"center"}
									justify={"space-between"}
								>
									<Group gap={"sm"}>
										{isReuploading && (
											<ActionIcon
												variant="subtle"
												onClick={() =>
													setIsReuploading(false)
												}
											>
												<IconArrowLeft size={24} />
											</ActionIcon>
										)}
										<Title order={2}>{description}</Title>
									</Group>
									<Flex align={"center"} justify={"flex-end"}>
										<Button
											variant="subtle"
											size="sm"
											leftSection={
												<IconInfoCircle size="1rem" />
											}
											onClick={() =>
												setShowInstructions(true)
											}
										>
											View Instructions
										</Button>
									</Flex>
								</Flex>
								<Box
									ref={dragRef}
									onDrop={handleDrop}
									onDragOver={handleDragOver}
									style={{
										border: "2px dashed var(--mantine-color-blue-4)",
										borderRadius:
											"var(--mantine-radius-md)",
										backgroundColor:
											"var(--mantine-color-blue-0)",
										textAlign: "center",
										cursor: "pointer",
										transition: "all 0.3s ease",
										height: "24rem",
										maxHeight: "calc(50vh - 120px)",
										flexGrow: 1,
									}}
									onClick={() =>
										fileInputRef.current?.click()
									}
								>
									<Stack
										gap="md"
										align="center"
										justify="center"
										h={"100%"}
										w={"100%"}
									>
										<ThemeIcon
											size="xl"
											variant="light"
											color="blue"
										>
											<IconUpload size="2rem" />
										</ThemeIcon>
										<Stack gap="xs" align="center">
											<Text size="lg" fw={500} c="blue">
												Drop your video here
											</Text>
											<Text size="sm" c="dimmed">
												or click to browse files
											</Text>
											<input
												ref={fileInputRef}
												type="file"
												accept="video/*"
												onChange={e =>
													e.target.files &&
													handleFileSelect(
														e.target.files[0]
													)
												}
												style={{ display: "none" }}
											/>
										</Stack>
									</Stack>
								</Box>
							</Stack>
							<Stack
								justify="center"
								align="center"
								mt={20}
								gap={10}
							>
								<RecordVideoModal
									onRecordClick={onRecordClick}
									setOnRecordClick={() =>
										setOnRecordClick(prev => !prev)
									}
									videoType={videoType}
									onVideoSelect={handleFileSelect}
								/>
							</Stack>
						</Paper>
					)}
				</Flex>
			</div>
		</div>
	);
};

export default VideoUpload;
