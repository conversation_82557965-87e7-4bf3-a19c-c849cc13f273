import type { profileStatusDataType } from "../types";

interface FallbackImageProps {
	firstName: string;
	lastName: string;
}

export function fallbackImage({
	firstName,
	lastName,
}: FallbackImageProps): string {
	if (
		!firstName ||
		!lastName ||
		/^\d/.test(firstName.charAt(0)) ||
		/^\d/.test(lastName.charAt(0))
	) {
		return `https://api.dicebear.com/5.x/initials/svg?seed=SM`;
	}

	return `https://api.dicebear.com/5.x/initials/svg?seed=${firstName.charAt(0)}${lastName.charAt(0)}`;
}

export function getProfileStatus(profileStatus: profileStatusDataType): string {
	switch (profileStatus) {
		case "onboarding":
			return "Onboarding";
		case "pending":
			return "Pending Review";
		case "re-approved":
			return "Awaiting Re-approval";
		case "approved":
			return "Approved";
		case "changes_requested":
			return "Changes Requested";
		default:
			return "Unknown";
	}
}

export function feedbackMessage(isRead?: boolean) {
	if (isRead === undefined) {
		return "No feedback provided";
	}
	if (isRead) {
		return "Seen by user";
	}
	return "User hasn’t seen feedback";
}
