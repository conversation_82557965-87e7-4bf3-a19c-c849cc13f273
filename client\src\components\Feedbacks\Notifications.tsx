import {
	Accordion,
	ActionIcon,
	Button,
	Flex,
	Image,
	Indicator,
	Loader,
	Menu,
	ScrollArea,
	Stack,
	Text,
	Title,
} from "@mantine/core";
import { Bell } from "lucide-react";
import { FeedbackCard } from "./FeedbackCard";
import empty from "../../assets/empty.svg";
import { useNotifications } from "../../contexts/NotificationContext";

const Notifications = () => {
	const {
		feedbacks,
		counts,
		loading,
		viewMoreLoading,
		toggleNotifications,
		loadMoreNotifications,
		decrementUnreadCount,
	} = useNotifications();

	return (
		<>
			<Menu
				shadow="md"
				width={400}
				position="bottom-end"
				offset={2}
				withArrow
			>
				<Menu.Target>
					<Indicator
						zIndex={1}
						disabled={counts.unreadCount === 0}
						size={20}
						label={
							counts.unreadCount > 9 ? "9+" : counts.unreadCount
						}
						color="red"
						offset={1}
						position="top-end"
					>
						<ActionIcon
							variant="subtle"
							size="lg"
							onClick={toggleNotifications}
						>
							<Bell className="w-5 h-5" />
						</ActionIcon>
					</Indicator>
				</Menu.Target>
				<Menu.Dropdown>
					<Menu.Label c={"gray.8"}>
						<Title order={3} c={"gray.8"}>
							Notifications
						</Title>
						<Menu.Divider />
					</Menu.Label>
					{loading ? (
						<Flex justify="center" align="center">
							<Loader />
						</Flex>
					) : (
						<ScrollArea h={400} p={12}>
							<Stack>
								{feedbacks && feedbacks.length > 0 ? (
									<Accordion
										multiple
										chevronPosition="right"
										variant="filled"
										style={{
											display: "flex",
											flexDirection: "column",
											gap: 12,
										}}
									>
										{(feedbacks ?? []).map(feedback => (
											<FeedbackCard
												key={feedback._id}
												feedback={feedback}
												setCounts={decrementUnreadCount}
												isFromNotification={true}
											/>
										))}
									</Accordion>
								) : (
									<Flex
										justify="center"
										align="center"
										direction="column"
									>
										<Image
											src={empty}
											alt="empty"
											w={140}
										/>
										<Text c={"gray"} fw={500}>
											You're all caught up!
										</Text>
									</Flex>
								)}

								{feedbacks &&
									!loading &&
									feedbacks.length < counts.totalCount && (
										<Button
											variant="subtle"
											loading={viewMoreLoading}
											onClick={loadMoreNotifications}
											w={"100%"}
										>
											View More
										</Button>
									)}
							</Stack>
						</ScrollArea>
					)}
				</Menu.Dropdown>
			</Menu>
		</>
	);
};

export default Notifications;
