import {
	Accordion,
	ActionIcon,
	Button,
	Flex,
	Image,
	Indicator,
	Loader,
	Menu,
	ScrollArea,
	Stack,
	Text,
	Title,
} from "@mantine/core";
import { Bell } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import apiClient from "../../config/axios";
import type { FeedbackDataType } from "../../types";
import { notifications } from "@mantine/notifications";
import { isAxiosError } from "axios";
import { FeedbackCard } from "./FeedbackCard";
import empty from "../../assets/empty.svg";

const Notifications = () => {
	const [notificationsOpened, setNotificationsOpened] =
		useState<boolean>(false);
	const [feedbacks, setFeedbacks] = useState<FeedbackDataType[]>([]);
	const [counts, setCounts] = useState<{
		totalCount: number;
		unreadCount: number;
	}>({ totalCount: 0, unreadCount: 0 });
	const [loading, setLoading] = useState(false);
	const [viewMoreLoading, setViewMoreLoading] = useState(false);

	const notificationHanlder = () => {
		setNotificationsOpened(!notificationsOpened);
	};

	const fetchFeedbacks = useCallback(
		async (isViewMore?: boolean, current_count?: number) => {
			if (isViewMore) {
				setViewMoreLoading(true);
			} else {
				setLoading(true);
			}
			try {
				const response = await apiClient.get(
					`/api/feedbacks/notifications?isViewMore=${isViewMore}&current_count=${current_count}`
				);
				setFeedbacks(prevFeedbacks => {
					const feedbackSet = new Set(
						(prevFeedbacks ?? []).map(feedback => feedback._id)
					);
					const newFeedbacks = response.data.data.filter(
						(feedback: FeedbackDataType) =>
							!feedbackSet.has(feedback._id)
					);
					return [...prevFeedbacks, ...newFeedbacks];
				});
				setCounts({
					totalCount: response.data.total,
					unreadCount: response.data.unreadCount,
				});
			} catch (error) {
				console.error(error);
				notifications.show({
					title: "Failed",
					message: isAxiosError(error)
						? error.response?.data?.message ||
							"Failed to fetch feedbacks"
						: "Failed to fetch feedbacks",
					color: "red",
				});
			} finally {
				if (isViewMore) {
					setViewMoreLoading(false);
				} else {
					setLoading(false);
				}
			}
		},
		[]
	);

	useEffect(() => {
		fetchFeedbacks(false, 0);
	}, [fetchFeedbacks]);

	return (
		<>
			<Menu
				shadow="md"
				width={400}
				position="bottom-end"
				offset={2}
				withArrow
			>
				<Menu.Target>
					<Indicator
						zIndex={1}
						disabled={counts.unreadCount === 0}
						size={20}
						label={
							counts.unreadCount > 9 ? "9+" : counts.unreadCount
						}
						color="red"
						offset={1}
						position="top-end"
					>
						<ActionIcon
							variant="subtle"
							size="lg"
							onClick={notificationHanlder}
						>
							<Bell className="w-5 h-5" />
						</ActionIcon>
					</Indicator>
				</Menu.Target>
				<Menu.Dropdown>
					<Menu.Label c={"gray.8"}>
						<Title order={3} c={"gray.8"}>
							Notifications
						</Title>
						<Menu.Divider />
					</Menu.Label>
					{loading ? (
						<Flex justify="center" align="center">
							<Loader />
						</Flex>
					) : (
						<ScrollArea h={400} p={12}>
							<Stack>
								{feedbacks && feedbacks.length > 0 ? (
									<Accordion
										multiple
										chevronPosition="right"
										variant="filled"
										style={{
											display: "flex",
											flexDirection: "column",
											gap: 12,
										}}
									>
										{(feedbacks ?? []).map(feedback => (
											<FeedbackCard
												key={feedback._id}
												feedback={feedback}
												setCounts={() => {
													setCounts(prev => ({
														...prev,
														unreadCount:
															prev.unreadCount -
															1,
													}));
												}}
												isFromNotification={true}
											/>
										))}
									</Accordion>
								) : (
									<Flex
										justify="center"
										align="center"
										direction="column"
									>
										<Image
											src={empty}
											alt="empty"
											w={140}
										/>
										<Text c={"gray"} fw={500}>
											You're all caught up!
										</Text>
									</Flex>
								)}

								{feedbacks &&
									!loading &&
									feedbacks.length < counts.totalCount && (
										<Button
											variant="subtle"
											loading={viewMoreLoading}
											onClick={() => {
												fetchFeedbacks(
													true,
													feedbacks.length
												);
											}}
											w={"100%"}
										>
											View More
										</Button>
									)}
							</Stack>
						</ScrollArea>
					)}
				</Menu.Dropdown>
			</Menu>
		</>
	);
};

export default Notifications;
