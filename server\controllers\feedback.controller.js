import {
	ONBOARDING_STEP,
	ProfileStatus,
	PUBLIC_EMAIL,
	PUBLIC_NAME,
	rolesValues,
} from "../constants/index.js";
import Feedback from "../models/Feedback.js";
import User from "../models/User.js";
import { sendEmail } from "../services/emaiService.js";
import { feedbackTemplate } from "../utils/emailTemplates.js";

export const sendFeedback = async (req, res) => {
	const { userId, feedback } = req.body;
	if (!userId || !feedback) {
		return res.status(400).json({ message: "Invalid request" });
	}

	if (userId === req.user._id.toString()) {
		return res
			.status(400)
			.json({ message: "You cannot send feedback to yourself" });
	}

	try {
		const user = await User.findById(userId);
		if (!user) {
			return res.status(404).json({ message: "User not found" });
		}
		if (user.displayStatus === false) {
			return res.status(404).json({ message: "User not found" });
		}

		const userEmail = user.email;

		const feedbackMessage = {
			basicDetails: feedback["basic-details"],
			earlyLife: feedback["early-life"],
			professionalLife: feedback["professional-life"],
			currentLife: feedback["current-life"],
		};
		const actionTaken = {
			basicDetails: feedback["basic-details"] ? false : undefined,
			earlyLife: feedback["early-life"] ? false : undefined,
			professionalLife: feedback["professional-life"] ? false : undefined,
			currentLife: feedback["current-life"] ? false : undefined,
		};
		const newFeedback = await Feedback.create({
			sender: req.user._id,
			readBy: [req.user._id],
			recipient: userId,
			feedbackMessage,
			actionTaken,
		});
		user.feedbacks.push(newFeedback._id);
		if (
			user.profileStatus === ProfileStatus.Pending &&
			user.onboardingStep === ONBOARDING_STEP.WAIT_FOR_APPROVAL
		) {
			user.profileStatus = ProfileStatus.ChangesRequested;
			user.onboardingStep = ONBOARDING_STEP.CURRENT_LIFE_FORM;
			user.resendToOnboarding = true;
		}
		await user.save();

		const fullName = `${user.firstName} ${user.secondName}`;
		await sendEmail({
			to: userEmail,
			subject: "Feedback on your profile",
			html: feedbackTemplate(fullName, feedback),
		});

		res.status(200).json({ message: "Feedback sent successfully" });
	} catch (error) {
		console.log(error);
		res.status(500).json({ message: error.message ?? "Server error" });
	}
};

export const getUserFeedbacks = async (req, res) => {
	const { userId } = req.params;
	const currentUserId = req.user._id;

	try {
		const user = await User.findById(userId)
			.select(
				"firstName middleName secondName email feedbacks displayStatus resendToOnboarding "
			)
			.lean();

		if (!user || !user.displayStatus) {
			return res.status(404).json({ message: "User not found" });
		}

		if (!user.feedbacks || user.feedbacks.length === 0) {
			return res.status(200).json({
				data: [],
				recipient: {
					_id: user._id,
					firstName: user.firstName,
					middleName: user.middleName,
					secondName: user.secondName,
					email: user.email,
				},
				resendToOnboarding: user.resendToOnboarding,
				total: 0,
				page: 1,
				totalPages: 0,
			});
		}

		const {
			page = 1,
			limit = 10,
			status = "active", // active, archived
			view = "all", // all, read, unread
			search = "",
			categories,
		} = req.query;

		const isActive = status === "active";

		const matchStage = {
			_id: { $in: user.feedbacks },
			isActive,
		};

		if (view === "read") {
			matchStage.readBy = user._id;
		} else if (view === "unread") {
			matchStage.readBy = { $ne: user._id };
		}

		const lookupStage = {
			$lookup: {
				from: "users",
				localField: "sender",
				foreignField: "_id",
				as: "sender",
				pipeline: [
					{ $project: { firstName: 1, secondName: 1, email: 1 } },
				],
			},
		};

		const unwindSenderStage = { $unwind: "$sender" };

		const searchMatchStage = {};
		if (search) {
			const escapedQuery = search.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
			const terms = escapedQuery.split(/\s+/);

			searchMatchStage.$and = terms.map(term => {
				const regex = new RegExp(term, "i");
				return {
					$or: [
						{ "sender.firstName": regex },
						{ "sender.middleName": regex },
						{ "sender.secondName": regex },
						{ "sender.email": regex },
						{ "feedbackMessage.basicDetails": regex },
						{ "feedbackMessage.earlyLife": regex },
						{ "feedbackMessage.professionalLife": regex },
						{ "feedbackMessage.currentLife": regex },
					],
				};
			});
		}

		const facetStage = {
			$facet: {
				metadata: [{ $count: "total" }],
				data: [
					{ $sort: { createdAt: -1 } },
					{ $skip: (page - 1) * limit },
					{ $limit: parseInt(limit) },
					{
						$addFields: {
							isReadByCurrentUser: {
								$in: [currentUserId, "$readBy"],
							},
							isReadByRecipient: {
								$in: ["$recipient", "$readBy"],
							},
						},
					},
					{
						$project: {
							readBy: 0,
							recipient: 0,
							isActive: 0,
							updatedAt: 0,
							actionTaken: 0,
							__v: 0, // optional if you also don’t want __v
						},
					},
				],
			},
		};

		const pipeline = [
			{ $match: matchStage },
			lookupStage,
			unwindSenderStage,
		];

		if (search) {
			pipeline.push({ $match: searchMatchStage });
		}
		if (categories) {
			const categoryList = categories.split(",");
			const categoryMatch = {
				$or: categoryList.map(category => ({
					[`feedbackMessage.${category}`]: {
						$exists: true,
						$ne: null,
					},
				})),
			};
			pipeline.push({ $match: categoryMatch });
		}
		pipeline.push(facetStage);

		const result = await Feedback.aggregate(pipeline);

		const metadata = result[0].metadata[0] || { total: 0 };
		const data = result[0].data;
		const totalPages = Math.ceil(metadata.total / limit);

		res.status(200).json({
			data,
			recipient: {
				_id: user._id,
				firstName: user.firstName,
				middleName: user.middleName,
				secondName: user.secondName,
				email: user.email,
			},
			resendToOnboarding: user.resendToOnboarding,
			total: metadata.total,
			page: parseInt(page),
			totalPages,
		});
	} catch (error) {
		console.log(error);
		res.status(500).json({ message: error.message ?? "Server error" });
	}
};

export const markFeedbackAsRead = async (req, res) => {
	try {
		const { feedbackId } = req.params;
		const userId = req.user._id;

		const feedback = await Feedback.findById(feedbackId);

		if (!feedback) {
			return res.status(404).json({ message: "Feedback not found" });
		}

		if (!feedback.readBy.includes(userId)) {
			feedback.readBy.push(userId);
			await feedback.save();
		}

		res.status(200).json({ message: "Feedback marked as read" });
	} catch (error) {
		console.log(error);
		res.status(500).json({ message: error.message ?? "Server error" });
	}
};

export const getNotifications = async (req, res) => {
	try {
		const userId = req.user._id;
		const { isViewMore = false, current_count = 0 } = req.query;

		const user = await User.findById(userId).select("feedbacks").lean();

		if (!user.feedbacks || user.feedbacks.length === 0) {
			return res.status(200).json({
				data: [],
				unreadCount: 0,
				totalCount: 0,
			});
		}

		const matchStage = {
			_id: { $in: user.feedbacks },
			isActive: true,
		};

		const limitCount = 3;
		const skipCount = isViewMore ? Number(current_count) : 0;

		const facetStage = {
			$facet: {
				metadata: [{ $count: "total" }],
				data: [
					{ $sort: { createdAt: -1 } },
					{ $skip: skipCount },
					{ $limit: limitCount },
					{
						$addFields: {
							isReadByCurrentUser: {
								$in: [userId, "$readBy"],
							},
							sender: {
								email: PUBLIC_EMAIL,
								firstName: PUBLIC_NAME,
								secondName: "Team",
							},
						},
					},
					{
						$project: {
							readBy: 0,
							isActive: 0,
							updatedAt: 0,
							"feedbackMessage.actionTaken": 0,
							__v: 0,
						},
					},
				],
				unreadCount: [
					{
						$match: {
							readBy: { $ne: userId },
						},
					},
					{ $count: "totalUnread" },
				],
			},
		};

		const pipeline = [{ $match: matchStage }, facetStage];
		const result = await Feedback.aggregate(pipeline);
		const dataToSend = {
			data: result[0].data,
			unreadCount: result[0].unreadCount[0]?.totalUnread || 0,
			total: result[0].metadata[0]?.total || 0,
		};

		res.status(200).json(dataToSend);
	} catch (error) {
		console.log(error);
		res.status(500).json({ message: error.message ?? "Server error" });
	}
};

export const getAllFeedbacks = async (req, res) => {
	const userId = req.user._id;
	const currentUserRole = req.user.role;

	const isAdmin = currentUserRole === rolesValues.Admin;

	try {
		const {
			page = 1,
			limit = 10,
			search = "",
			status = "active",
		} = req.query;

		const pageNum = parseInt(page, 10);
		const limitNum = parseInt(limit, 10);

		const escapedSearch = search.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
		const terms = escapedSearch.trim() ? escapedSearch.split(/\s+/) : [];

		// Build search conditions
		const searchConditions = terms.map(term => {
			const regex = new RegExp(term, "i");
			return {
				$or: [
					{ "user.firstName": regex },
					{ "user.middleName": regex },
					{ "user.secondName": regex },
					{ "user.email": regex },
				],
			};
		});

		const pipeline = [
			{
				$lookup: {
					from: "users",
					localField: "sender",
					foreignField: "_id",
					as: "sender",
				},
			},
			{ $unwind: "$sender" },
			{
				$lookup: {
					from: "users",
					localField: "_id",
					foreignField: "feedbacks",
					as: "user",
					pipeline: [
						{
							$match: {
								displayStatus: true,
								role: isAdmin
									? {
											$in: [
												rolesValues.Admin,
												rolesValues.CommunityMember,
											],
										}
									: {
											$in: [
												rolesValues.SuperAdmin,
												rolesValues.Admin,
												rolesValues.CommunityMember,
											],
										},
							},
						},
						{
							$project: {
								firstName: 1,
								middleName: 1,
								secondName: 1,
								email: 1,
								image: 1,
								role: 1,
								profileStatus: 1,
							},
						},
					],
				},
			},
			{
				$unwind: {
					path: "$user",
				},
			},
			{ $match: { "user._id": { $ne: userId } } },
			{
				$match: {
					isActive: status === "active" ? true : false,
					...(terms.length > 0 ? { $and: searchConditions } : {}),
				},
			},
			{
				$group: {
					_id: "$user._id",
					feedbackCount: { $sum: 1 },
					user: { $first: "$user" },
					latestFeedbackDate: { $max: "$createdAt" },
				},
			},
			{
				$sort: { latestFeedbackDate: -1 },
			},
			{
				$facet: {
					metadata: [{ $count: "total" }],
					data: [
						{ $skip: (pageNum - 1) * limitNum },
						{ $limit: limitNum },
						{
							$project: {
								_id: 0,
								user: 1,
								feedbackCount: 1,
							},
						},
					],
				},
			},
		];

		const result = await Feedback.aggregate(pipeline);

		const metadata = result[0]?.metadata[0] || { total: 0 };
		const data = result[0]?.data || [];
		const totalPages = Math.ceil(metadata.total / limitNum);

		res.status(200).json({
			data,
			total: metadata.total, // This is now the total of unique users
			page: pageNum,
			totalPages,
		});
	} catch (error) {
		console.error("Error in getting all feedbacks:", error);
		res.status(500).json({ message: "Server error" });
	}
};
