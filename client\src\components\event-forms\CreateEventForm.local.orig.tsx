import {
	<PERSON><PERSON>,
	Container,
	<PERSON><PERSON>,
	Textarea,
	TextInput,
	Title,
	Text,
	Card,
	Flex,
	Grid,
	Image,
	ActionIcon,
	Group,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { DateTimePicker } from "@mantine/dates";
import type React from "react";
import apiClient from "../../config/axios";
import { notifications } from "@mantine/notifications";
import { IconCheck, IconUpload, IconX } from "@tabler/icons-react";
import { isAxiosError } from "axios";
import { useRef, useState } from "react";
import { CalendarDays, FileText, ImageIcon, Save } from "lucide-react";
import { Dropzone, type FileWithPath } from "@mantine/dropzone";
import { AnimatePresence, motion } from "framer-motion";

type EventFormValues = {
	name: string;
	description: string;
	startDate: Date | null;
	endDate: Date | null;
};

type EventFormProps = {
	initialValues?: Partial<EventFormValues>;
	onSubmit?: (values: EventFormValues) => void;
};

export const CreateEventForm: React.FC = (props: EventFormProps) => {
	const fieldRefs = useRef<Record<string, HTMLElement | null>>({});

	const [loading, setLoading] = useState<boolean>(false);
	const [images, setImages] = useState<FileWithPath[]>([]);

	const setFieldRef = (field: string) => (el: HTMLElement | null) => {
		fieldRefs.current[field] = el;
	};

	const scrollToError = (field: string) => {
		const ref = fieldRefs.current[field];
		if (ref) {
			ref.scrollIntoView({ behavior: "smooth", block: "center" });
			if ("focus" in ref)
				(ref as HTMLElement).focus({ preventScroll: true });
		}
	};

	const handleSubmit = async (values: EventFormValues) => {
		const validation = form.validate();
		if (validation.hasErrors) {
			const firstError = Object.keys(validation.errors)[0];
			scrollToError(firstError);
			return;
		}

		setLoading(true);
		if (!values.startDate || !values.endDate) {
			throw new Error("Dates are required");
		}
		const formData = new FormData();
		formData.append("name", values.name);
		formData.append("description", values.description);
		formData.append("startDate", new Date(values.startDate).toISOString());
		formData.append("endDate", new Date(values.endDate).toISOString());

		images.forEach(img => {
			formData.append("images", img);
		});
		try {
			const res = await apiClient.post("/api/events", formData, {
				headers: {
					"Content-Type": "multipart/form-data",
				},
			});
			notifications.show({
				title: "Success",
				message: res.data.message || "Event created successfully",
				color: "green",
				icon: <IconCheck />,
			});
			form.reset();
			setImages([]);
		} catch (err) {
			if (isAxiosError(err)) {
				notifications.show({
					title: "Unable to create event",
					message:
						err.response?.data?.message || "Can't create event",
					color: "red",
					icon: <IconX />,
				});
			}
		} finally {
			setLoading(false);
		}
	};

	const form = useForm<EventFormValues>({
		initialValues: {
			name: props.initialValues?.name || "",
			description: props.initialValues?.description || "",
			startDate: props.initialValues?.startDate || null,
			endDate: props.initialValues?.endDate || null,
		},
		validate: {
			name: val => (val.trim() === "" ? "Name is required" : null),
			description: val =>
				val.trim() === "" ? "Description is required" : null,
			startDate: (value, values) => {
				if (!value) return "Start date is required";

				const dateValue =
					value instanceof Date ? value : new Date(value);

				const now = new Date();

				if (dateValue.getTime() < now.getTime()) {
					return "Start date cannot be in the past";
				}

				if (
					values.startDate &&
					values.endDate &&
					values.startDate >= values.endDate
				) {
					return "Start date must be before End Date";
				}

				return null;
			},
			endDate: (value, values) =>
				!value
					? "End date is required"
					: values.startDate && value < values.startDate
						? "End date cannot be before start date"
						: null,
		},
		transformValues: values => ({
			...values,
			name: values.name.trim(),
			description: values.description.trim(),
		}),
	});

	return (
		<Container>
			<Stack mb="md" gap={0}>
				<Title order={1}>Create New Event</Title>
				<Text c="dimmed">
					Fill out the form below to create a new event with images
					and scheduling details.
				</Text>
			</Stack>

			<form
				onSubmit={e => {
					e.preventDefault();
					handleSubmit(form.values);
				}}
			>
				<Stack>
					<Card shadow="lg" p="lg" withBorder>
						<Stack>
							<Flex align="center" gap="xs">
								<FileText size={20} />
								<Title order={3}>Basic Information</Title>
							</Flex>
							<TextInput
								ref={setFieldRef("name")}
								label="Event Name"
								placeholder="Enter event name"
								{...form.getInputProps("name")}
								error={
									form.errors["name"]
										? form.errors["name"]
										: undefined
								}
								withAsterisk
							/>

							<Textarea
								ref={setFieldRef("description")}
								label="Description"
								placeholder="Enter event description"
								{...form.getInputProps("description")}
								error={
									form.errors["description"]
										? form.errors["description"]
										: undefined
								}
								withAsterisk
								styles={{
									input: {
										height: "140px",
									},
								}}
							/>
						</Stack>
					</Card>

					<Card shadow="lg" p="lg" withBorder>
						<Stack>
							<Flex align="center" gap="xs">
								<CalendarDays size={20} />
								<Title order={3}>Schedule</Title>
							</Flex>
							<Flex
								align="center"
								justify="space-between"
								gap="md"
							>
								<DateTimePicker
									ref={setFieldRef("startDate")}
									style={{ flex: 1 }}
									label="Start Date and Time"
									placeholder="Pick start date"
									{...form.getInputProps("startDate")}
									withAsterisk
									hideOutsideDates={true}
									weekendDays={[]}
									timePickerProps={{
										withDropdown: true,
										format: "24h",
									}}
								/>

								<DateTimePicker
									ref={setFieldRef("endDate")}
									style={{ flex: 1 }}
									label="End Date and Time"
									placeholder="Pick end date"
									{...form.getInputProps("endDate")}
									withAsterisk
									hideOutsideDates={true}
									weekendDays={[]}
									timePickerProps={{
										withDropdown: true,
										format: "24h",
									}}
								/>
							</Flex>
						</Stack>
					</Card>

					<Card shadow="lg" p="lg" withBorder>
						<Images images={images} setImages={setImages} />
					</Card>

					<Flex justify="flex-end">
						<Button
							size="sm"
							loading={loading}
							type="submit"
							leftSection={<Save size={16} />}
						>
							Create Event
						</Button>
					</Flex>
				</Stack>
			</form>
		</Container>
	);
};

const Images = ({
	images,
	setImages,
}: {
	images: FileWithPath[];
	setImages: React.Dispatch<React.SetStateAction<FileWithPath[]>>;
}) => {
	const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
	const [dropzoneLoading, setDropzoneLoading] = useState<boolean>(false);

	const handleDrop = (files: FileWithPath[]) => {
		setDropzoneLoading(true);
		setImages(prev => {
			const existingKeys = new Set(
				prev.map(img => `${img.name}-${img.size}-${img.lastModified}`)
			);

			let haveDuplicateImages = false;

			const newFiles = files.filter(file => {
				const fileKey = `${file.name}-${file.size}-${file.lastModified}`;
				if (existingKeys.has(fileKey)) {
					haveDuplicateImages = true;
					return false;
				}
				return true;
			});

			if (haveDuplicateImages) {
				notifications.show({
					title: "Duplicate Images",
					message: "Duplicate images are not allowed.",
					color: "red",
					icon: <IconX />,
				});
			}

			return [...prev, ...newFiles];
		});
		setDropzoneLoading(false);
	};

	const handleReject = () => {
		notifications.show({
			title: "Invalid File",
			message: "Only image files are allowed.",
			color: "red",
			icon: <IconX />,
		});
	};

	const removeImage = (index: number) => {
		setImages(prevImages => prevImages.filter((_, i) => i !== index));
	};

	return (
		<>
			<Stack>
				<Flex align="center" gap="xs">
					<ImageIcon size={20} />
					<Title order={3}>Images</Title>
				</Flex>

				{/* Upload Images */}
				<Dropzone
					loading={dropzoneLoading}
					onDrop={handleDrop}
					onReject={handleReject}
					accept={{
						"image/*": [],
					}}
					multiple
				>
					<Stack
						align="center"
						justify="center"
						gap="xs"
						mih={200}
						style={{ pointerEvents: "none" }}
					>
						<Text size="xl" inline>
							Drag images here or click to select files
						</Text>
						<Text size="sm" c="dimmed" inline mt={7}>
							Attach as many files as you like
						</Text>
						<Button
							mt="sm"
							style={{
								color: "var(--mantine-color-gray-6)",
								backgroundColor: "var(--mantine-color-gray-2)",
								height: "45px",
								borderRadius: "6px",
							}}
							styles={{
								root: {
									padding: "12px 24px",
								},
							}}
							leftSection={<ImageIcon size={16} />}
						>
							Choose Images
						</Button>
					</Stack>
				</Dropzone>

				{/* Preview Images */}
				{images.length > 0 && (
					<Stack mt="md">
						<Text fw={500}>Selected Images ({images.length})</Text>
						<Grid gutter="lg">
							{images.map((img, index) => {
								const imageKey = `${img.name}-${img.size}-${img.lastModified}`;

								return (
									<Grid.Col key={imageKey} span={3}>
										<AnimatePresence mode="popLayout">
											<motion.div
												key={imageKey}
												initial={{
													opacity: 0,
													scale: 1,
												}}
												animate={{
													opacity: 1,
													scale: 1,
												}}
												exit={{
													opacity: 0,
													scale: 0.9,
												}}
												transition={{
													duration: 0.3,
													delay: index * 0.05,
												}}
												layout
											>
												<Card
													shadow={
														hoveredIndex === index
															? "lg"
															: "sm"
													}
													radius="8px"
													p={8}
													withBorder
													style={{
														width: "200px",
														position: "relative",
													}}
													onMouseEnter={() =>
														setHoveredIndex(index)
													}
													onMouseLeave={() =>
														setHoveredIndex(null)
													}
												>
													<Group>
														<Image
															src={URL.createObjectURL(
																img
															)}
															alt="Preview"
															style={{
																objectFit:
																	"cover",
																width: "250px",
																height: "200px",
																filter:
																	hoveredIndex ===
																	index
																		? "brightness(90%)"
																		: "brightness(100%)",
																cursor: "pointer",
															}}
															styles={{
																root: {
																	borderRadius:
																		"8px",
																},
															}}
														/>

														{/* Delete button */}
														<ActionIcon
															color="red"
															variant="filled"
															radius="xl"
															size="md"
															onClick={() =>
																removeImage(
																	index
																)
															}
															style={{
																position:
																	"absolute",
																top: 12,
																right: 8,
																opacity:
																	hoveredIndex ===
																	index
																		? 1
																		: 0,
																transition:
																	"opacity 0.2s",
																pointerEvents:
																	hoveredIndex ===
																	index
																		? "auto"
																		: "none",
															}}
														>
															<IconX size={18} />
														</ActionIcon>

														{/* Replace button */}
														<ActionIcon
															color="var(--mantine-color-blue-4)"
															radius="xl"
															size="md"
															onClick={() =>
																console.log(
																	"Replace Image is Coming Soon"
																)
															}
															style={{
																position:
																	"absolute",
																top: 44,
																right: 8,
																opacity:
																	hoveredIndex ===
																	index
																		? 1
																		: 0,
																transition:
																	"opacity 0.2s",
																pointerEvents:
																	hoveredIndex ===
																	index
																		? "auto"
																		: "none",
															}}
														>
															<IconUpload
																size={18}
															/>
														</ActionIcon>
													</Group>
												</Card>
											</motion.div>
										</AnimatePresence>
									</Grid.Col>
								);
							})}
						</Grid>
					</Stack>
				)}
			</Stack>
		</>
	);
};

export default Images;
